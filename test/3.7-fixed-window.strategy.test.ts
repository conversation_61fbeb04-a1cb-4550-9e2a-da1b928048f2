import type { StrategyEvents } from '../app/utils/limiter/strategies/strategy'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import { Emitter } from '@kdt310722/utils/event'
import { FixedWindowStrategy } from '../app/utils/limiter/strategies/fixed-window'

// Set up correct working directory for imports
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
process.chdir(path.join(__dirname, '..'))

// Test utilities
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

function assert(condition: boolean, message: string): void {
    if (!condition) {
        throw new Error(`Assertion failed: ${message}`)
    }

    console.log(`✅ PASSED: ${message}`)
}

/**
 * Main test function for FixedWindowStrategy
 */
async function runTest() {
    console.log('\n=== Testing FixedWindowStrategy Event Logic ===\n')

    // Configuration
    const CONFIG = {
        capacity: 3, // Allow 3 requests per window
        window: 1000, // Window is 1 second (1000ms)
    }

    // Test trackers
    let limitEventCount = 0
    let releaseEventCount = 0
    let lastUntil: Date | null = null
    let limitEventMetadata: Record<string, any> | null = null
    const eventSequence: string[] = []

    // Create emitter to track events
    const emitter = new Emitter<StrategyEvents>()

    // Set up event listeners
    emitter.on('limit', (until, metadata) => {
        const now = Date.now()
        console.log(`[${now}] 🛑 EVENT 'limit' fired. Will reset at: ${until.toISOString()}`)
        limitEventCount++
        lastUntil = until
        limitEventMetadata = metadata
        eventSequence.push('limit')
    })

    emitter.on('release', () => {
        const now = Date.now()
        console.log(`[${now}] 🟢 EVENT 'release' fired.`)
        releaseEventCount++
        eventSequence.push('release')
    })

    emitter.on('error', (error) => {
        console.error('❌ ERROR event:', error)
    })

    // Create strategy instance
    const strategy = new FixedWindowStrategy(CONFIG, emitter)

    // Test Case 1: Verify 'limit' event fires exactly once when capacity is reached
    console.log('\n--- Test Case 1: Triggering limit event ---')
    console.log(`Sending ${CONFIG.capacity + 2} requests (capacity: ${CONFIG.capacity})...`)

    const startTime = Date.now()

    // Send requests (capacity + 2) to ensure we hit the limit
    const requests = []

    for (let i = 0; i < CONFIG.capacity + 2; i++) {
        const request = strategy.schedule(async () => {
            console.log(`[${Date.now()}] Request #${i + 1} processed`)
            await delay(50) // Simulate work

            return i + 1
        }).catch((error) => {
            console.log(`[${Date.now()}] Request #${i + 1} rejected: ${error.message}`)
        })

        requests.push(request)

        // Small delay to ensure we get consistent ordering for testing
        await delay(10)
    }

    // Wait for all requests to complete
    await Promise.allSettled(requests)

    // Analyze results for Test Case 1
    console.log('\n--- Analyzing Test Case 1 Results ---')

    try {
        // 1. Verify limit event fired exactly once
        assert(limitEventCount === 1, '\'limit\' event should fire exactly once when capacity is reached')

        // 2. Verify the 'until' parameter is correct
        assert(lastUntil instanceof Date, '\'until\' parameter should be a Date object')

        if (lastUntil) {
            const expectedWindowEnd = startTime + CONFIG.window
            const tolerance = 100 // Allow 100ms tolerance for timing

            assert(
                Math.abs(lastUntil.getTime() - expectedWindowEnd) <= tolerance,
                `'until' date should be approximately ${new Date(expectedWindowEnd).toISOString()}`,
            )
        }

        // 3. Verify the metadata object exists
        assert(limitEventMetadata !== null, '\'limit\' event should include metadata object')

        // 4. Verify no release event has fired yet in this window
        const hasReleaseAfterLimit = eventSequence.includes('release') &&
            eventSequence.includes('release') &&
            eventSequence.includes('limit')

        if (hasReleaseAfterLimit) {
            console.log('\n⚠️ IMPLEMENTATION ISSUE DETECTED:')
            console.log('The \'release\' event was fired immediately after the \'limit\' event in the same window.')
            console.log('According to requirements, \'release\' should only fire when the limiter starts processing')
            console.log('a new request AFTER a \'limit\' event has occurred in a PREVIOUS window.')
            console.log('\nThe problem is in the handleExecuting() method of FixedWindowStrategy.')
            console.log('It emits \'release\' event whenever a request starts executing if isLimited=true,')
            console.log('without checking if we\'re still in the same window where \'limit\' was triggered.')

            console.log('\n💡 SUGGESTED FIX:')
            console.log('1. Add a property to track the window end time when \'limit\' was triggered')
            console.log('2. Only emit \'release\' if current time > limitedWindowEndTime')
            console.log('3. Example implementation fix:')

            console.log(`
    // Add this property to the class
    protected limitedWindowEndTime = 0

    protected handleDepleted(config: FixedWindowStrategyConfig) {
        if (this.isLimited) {
            return
        }

        this.isLimited = true

        const elapsed = Date.now() - this.createdAt
        const currentWindowEnd = this.createdAt + (Math.ceil(elapsed / config.window) * config.window)
        this.limitedWindowEndTime = currentWindowEnd
        const until = new Date(currentWindowEnd)

        this.emitter.emit('limit', until, {})
    }

    protected handleExecuting() {
        if (this.isLimited && Date.now() > this.limitedWindowEndTime) {
            this.emitter.emit('release')
            this.isLimited = false
        }
    }`)

            throw new Error('\'release\' event should not fire during the limited window')
        }

        console.log('✅ Test Case 1 PASSED')
    } catch (error) {
        console.error(`❌ Test Case 1 FAILED: ${error}`)
        process.exit(1)
    }

    // Test Case 2: Verify 'release' event fires when processing requests after limit
    console.log('\n--- Test Case 2: Verifying release event ---')
    console.log(`Waiting for window to reset (${CONFIG.window + 200}ms)...`)

    // Wait for the rate limit window to reset, plus some buffer
    await delay(CONFIG.window + 200)

    console.log('Sending another request after window reset...')

    // Send a single request after the window resets
    await strategy.schedule(async () => {
        console.log(`[${Date.now()}] Request after window reset processed`)
        await delay(50)
    })

    // Analyze results for Test Case 2
    console.log('\n--- Analyzing Test Case 2 Results ---')

    try {
        // 1. Verify limit event count hasn't changed
        assert(limitEventCount === 1, '\'limit\' event count should still be 1 (not triggered again)')

        // 2. Verify release event fired exactly once
        assert(releaseEventCount === 1, '\'release\' event should fire exactly once after limit')

        console.log('✅ Test Case 2 PASSED')
    } catch (error) {
        console.error(`❌ Test Case 2 FAILED: ${error}`)
        process.exit(1)
    }

    // Test Case 3: Verify limit event fires again if capacity is reached in a new window
    console.log('\n--- Test Case 3: Verifying limit event in new window ---')

    // Reset counters for this test case
    limitEventCount = 0
    releaseEventCount = 0
    lastUntil = null

    console.log(`Sending ${CONFIG.capacity + 2} requests in new window...`)

    const newWindowStartTime = Date.now()

    // Send enough requests to hit the limit again
    const newRequests = []

    for (let i = 0; i < CONFIG.capacity + 2; i++) {
        const request = strategy.schedule(async () => {
            console.log(`[${Date.now()}] New window request #${i + 1} processed`)
            await delay(50)

            return i + 1
        }).catch((error) => {
            console.log(`[${Date.now()}] New window request #${i + 1} rejected: ${error.message}`)
        })

        newRequests.push(request)
        await delay(10)
    }

    // Wait for all new requests to complete
    await Promise.allSettled(newRequests)

    // Analyze results for Test Case 3
    console.log('\n--- Analyzing Test Case 3 Results ---')

    try {
        // 1. Verify limit event fired again
        assert(limitEventCount === 1, '\'limit\' event should fire again in the new window')

        // 2. Verify the new 'until' parameter
        if (lastUntil) {
            const expectedNewWindowEnd = newWindowStartTime + CONFIG.window
            const tolerance = 100

            assert(
                Math.abs(lastUntil.getTime() - expectedNewWindowEnd) <= tolerance,
                `New 'until' date should be approximately ${new Date(expectedNewWindowEnd).toISOString()}`,
            )
        }

        console.log('✅ Test Case 3 PASSED')
    } catch (error) {
        console.error(`❌ Test Case 3 FAILED: ${error}`)
        process.exit(1)
    }

    console.log('\n=== All Tests PASSED ===\n')
}

// Run the test
runTest().catch((error) => {
    console.error('❌ Unexpected test error:', error)
    process.exit(1)
})
