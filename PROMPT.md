### **Prompt for AI Coding Assistant**

**Objective:**
Your primary task is to create a single, framework-free test file named `test/fixed-window.strategy.test.ts` to rigorously test the event-emitting logic of the `FixedWindowStrategy` class. After generating the test file, you must execute it and analyze the results to verify its correctness or diagnose failures.

**Context:**
The system consists of three main components that you must read and understand from the project source code:

1.  **`app/utils/limiter/strategies/strategy.ts`**: The abstract base class.
2.  **`app/utils/limiter/limiter.ts`**: The consumer class that uses the strategy.
3.  **`app/utils/limiter/strategies/fixed-window.ts`**: The concrete implementation using `bottleneck`, which is the primary class under test.

You should assume that `FixedWindowStrategy` has already been modified to include the logic for emitting custom `limit` and `release` events, as this is the core functionality you need to verify.

**Critical Test Requirements:**

1.  **Event `limit`:**

    - This event **must** fire the first time the capacity is reached within a given window.
    - It **must not** fire for subsequent requests that are rejected within the same limited window.
    - The `until` parameter of the event must be a `Date` object that correctly represents the time when the next window begins (approximately `Date.now() + window`).
    - The event should fire again in the next window if the limit is reached again.

2.  **Event `release`:**
    - This event **must** fire when the limiter starts processing a new request after a `limit` event has occurred.
    - It **must only** fire if a `limit` event was previously fired in the preceding window.

**Technical Specifications:**

- **File Path:** Create the test file at `test/fixed-window.strategy.test.ts`.
- **No Frameworks:** Do not use any external testing frameworks like Jest, Mocha, Vitest, etc. Use plain TypeScript/JavaScript with standard `async/await`, `Promise`, `setTimeout`, and `console.log` / `console.assert` to structure the test and report results.
- **Asynchronous Nature:** The test must correctly handle the asynchronous behavior of the limiter. Use `setTimeout` to wait for windows to reset.
- **Test Logic:** The test should simulate a burst of requests that exceeds the limiter's capacity, wait for the window to reset, and then send more requests to verify the `release` logic.

**Execution and Analysis Plan:**

1.  **Generate Code:** Write the complete code for `test/fixed-window.strategy.test.ts`.
2.  **Execute Test:** After writing the file, provide the command to run it. The correct command is:
    ```bash
    tsx test/fixed-window.strategy.test.ts
    ```
3.  **Analyze Output:**
    - Run the command and capture the output.
    - Analyze the console logs to confirm if the events (`limit`, `release`) were fired in the correct order, at the right times, and with the correct parameters.
    - The output should clearly state whether each part of the test "PASSED" or "FAILED".
4.  **Diagnose Failures:**
    - If the test fails, determine the root cause.
    - If the failure is due to a flaw in the test logic, correct the test file.
    - If the failure is due to an issue in the `FixedWindowStrategy` class implementation, clearly explain **why** it is failing based on the requirements. Provide detailed, step-by-step guidance on how to fix the `FixedWindowStrategy` class, but **do not** modify its code directly.
