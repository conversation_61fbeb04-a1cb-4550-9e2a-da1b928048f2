import type { EndpointConfig } from '../config/endpoints'
import { highlight, type Logger } from '@kdt310722/logger'
import { formatDate } from '@kdt310722/utils/time'
import { createChildLogger } from '../core/logger'
import { Limiter } from '../utils/limiter/limiter'
import { Sender } from '../utils/sender/sender'

export class Endpoint {
    public readonly name: string

    protected readonly logger: Logger
    protected readonly sender: Sender
    protected readonly limiter: Limiter

    public constructor(protected readonly config: EndpointConfig) {
        this.name = config.name
        this.logger = createChildLogger(`endpoints:${config.name}`)
        this.sender = this.createSender(config.http)
        this.limiter = this.createLimiter(config.http)
    }

    protected createSender(config: EndpointConfig['http']) {
        return new Sender(config.url, config)
    }

    protected createLimiter({ maxRequestsPerSecond }: EndpointConfig['http']) {
        const limiter = new Limiter({ maxRequestsPerSecond })

        limiter.on('limit', this.handleRateLimit.bind(this))
        limiter.on('error', this.handleLimiterError.bind(this))

        return limiter
    }

    protected handleRateLimit(until: Date) {
        this.logger.warn(`Rate limit reached for endpoint ${highlight(this.name)} until ${highlight(formatDate(until, true))}`)
    }

    protected handleLimiterError(error: unknown) {
        this.logger.error(`Error occurred in limiter for endpoint ${highlight(this.name)}`, error)
    }
}
