import type { Awaitable } from '@kdt310722/utils/promise'
import type { LimiterScheduleOptions, Strategy, StrategyEvents } from './strategies/strategy'
import { Emitter } from '@kdt310722/utils/event'
import { FixedWindowStrategy } from './strategies/fixed-window'
import { NoopStrategy } from './strategies/noop'

export interface LimiterConfig {
    maxRequestsPerSecond?: number
}

export class Limiter extends Emitter<StrategyEvents> {
    protected readonly strategy: Strategy

    public constructor(config: LimiterConfig) {
        super()

        this.strategy = this.getStrategy(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, options?: LimiterScheduleOptions) {
        return this.strategy.schedule(fn, options)
    }

    protected getStrategy(config: LimiterConfig): Strategy {
        if (config.maxRequestsPerSecond) {
            return new FixedWindowStrategy({ capacity: config.maxRequestsPerSecond, window: 1000 }, this)
        }

        return new NoopStrategy({}, this)
    }
}
