import { highlight } from '@kdt310722/logger'
import { sleep } from '@kdt310722/utils/promise'
import { Limiter } from './app/utils/limiter/limiter'

let count = 0

const limiter = new Limiter({
    maxRequestsPerSecond: 3,
})

limiter.on('limit', (until) => {
    console.log('Limit until:', highlight(until.toISOString()))
})

limiter.on('release', () => {
    console.log('Release')
})

const fake = async () => limiter.schedule(async () => {
    console.log('Request start:', ++count, highlight(new Date().toISOString()))

    await sleep(1000)
})

const fakeBulk = async () => {
    await Promise.all([
        fake(),
        fake(),
        fake(),
        fake(),
        fake(),
    ])
}

for (let i = 0; i < 5; i++) {
    fakeBulk()
}
